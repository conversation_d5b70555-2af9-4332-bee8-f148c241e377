document.addEventListener("DOMContentLoaded", () => {
    document.querySelector(".split-screen.yes")
        .addEventListener("wheel", (e) => {
            e.stopImmediatePropagation();
        }, { passive: false });
});

window.addEventListener("load", function() {
    const container = document.querySelector(".split-screen.yes");
    if (!container) return;

    function setMaxHeight() {
        let maxHeight = 0;

        container.childNodes.forEach(child => {
            if (child.nodeType === 1) { // tylko elementy
                const h = child.offsetHeight;
                if (h > maxHeight) {
                    maxHeight = h;
                }
            }
        });

        // ustawiamy wysoko<PERSON>ć z najwyższym priorytetem
        container.style.setProperty("height", maxHeight + "px", "important");
    }

    // pierwsze ustawienie po załadowaniu
    setMaxHeight();

    // aktualizacja przy zmianie rozmiaru okna
    window.addEventListener("resize", setMaxHeight);

    // obserwujemy zmiany w drzewie DOM i atrybutach
    const observer = new MutationObserver(() => {
        setMaxHeight();
    });

    observer.observe(container, {
        attributes: true,
        childList: true,
        subtree: true
    });
});